{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "dependencies": {}, "description": "deterministic `JSON.stringify()` - a faster version of substack's json-stable-strigify without jsonify", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.0.0", "eslint": "^6.7.0", "fast-stable-stringify": "latest", "faster-stable-stringify": "latest", "json-stable-stringify": "latest", "nyc": "^14.1.0", "pre-commit": "^1.2.2", "tape": "^4.11.0"}, "homepage": "https://github.com/epoberezkin/fast-json-stable-stringify", "license": "MIT", "main": "index.js", "name": "fast-json-stable-stringify", "repository": {"type": "git", "url": "git://github.com/epoberezkin/fast-json-stable-stringify.git"}, "types": "index.d.ts", "version": "2.1.0"}