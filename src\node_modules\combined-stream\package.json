{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "dependencies": {"delayed-stream": "~1.0.0"}, "description": "A stream that emits multiple other streams one after another.", "devDependencies": {"far": "~0.0.7"}, "engines": {"node": ">= 0.8"}, "homepage": "https://github.com/felixge/node-combined-stream", "license": "MIT", "main": "./lib/combined_stream", "name": "combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "version": "1.0.8"}