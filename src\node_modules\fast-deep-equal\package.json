{"author": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Fast deep equal", "devDependencies": {"coveralls": "^3.1.0", "dot": "^1.1.2", "eslint": "^7.2.0", "mocha": "^7.2.0", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "react": "^16.12.0", "react-test-renderer": "^16.12.0", "sinon": "^9.0.2", "typescript": "^3.9.5"}, "files": ["index.js", "index.d.ts", "react.js", "react.d.ts", "es6/"], "homepage": "https://github.com/epoberezkin/fast-deep-equal#readme", "license": "MIT", "main": "index.js", "name": "fast-deep-equal", "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/fast-deep-equal.git"}, "types": "index.d.ts", "version": "3.1.3"}