{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless#readme", "license": "Apache-2.0", "main": "index.js", "name": "caseless", "repository": {"type": "git", "url": "git+https://github.com/mikeal/caseless.git"}, "test": "node test.js", "version": "0.12.0"}