{"author": {"name": "<PERSON><PERSON><PERSON>"}, "bin": {"kill-port": "cli.js"}, "dependencies": {"get-them-args": "1.3.2", "shell-exec": "1.0.2"}, "description": "Kill the process running on given port", "devDependencies": {"husky": "4.2.5", "jest": "26.1.0", "npm-check": "^5.9.2", "standard": "14.3.4"}, "homepage": "https://github.com/tiaanduplessis/kill-port", "license": "MIT", "main": "index.js", "name": "kill-port", "repository": {"type": "git", "url": "git+https://github.com/tiaanduplessis/kill-port.git"}, "standard": {"env": {"jest": true}}, "version": "1.6.1"}