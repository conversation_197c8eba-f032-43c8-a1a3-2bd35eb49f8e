{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "description": "YAML 1.2 parser and serializer", "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^7.0.0", "fast-check": "^1.24.2", "istanbul": "^0.4.5", "mocha": "^7.1.2", "uglify-js": "^3.0.1"}, "files": ["index.js", "lib/", "bin/", "dist/"], "homepage": "https://github.com/nodeca/js-yaml", "jsdelivr": "dist/js-yaml.min.js", "license": "MIT", "name": "js-yaml", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "unpkg": "dist/js-yaml.min.js", "version": "3.14.1"}