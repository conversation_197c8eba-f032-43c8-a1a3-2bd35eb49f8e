{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Create and parse HTTP Content-Type header", "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-type#readme", "license": "MIT", "name": "content-type", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-type.git"}, "version": "1.0.4"}