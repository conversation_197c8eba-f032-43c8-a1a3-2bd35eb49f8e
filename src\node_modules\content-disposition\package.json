{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"safe-buffer": "5.1.2"}, "description": "Create and parse Content-Disposition header", "devDependencies": {"deep-equal": "1.0.1", "eslint": "5.10.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-rc.1", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "5.2.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-disposition#readme", "license": "MIT", "name": "content-disposition", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-disposition.git"}, "version": "0.5.3"}