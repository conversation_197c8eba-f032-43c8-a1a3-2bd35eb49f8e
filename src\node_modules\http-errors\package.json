{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "description": "Create HTTP error objects", "devDependencies": {"eslint": "5.13.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "5.2.0"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "homepage": "https://github.com/jshttp/http-errors#readme", "license": "MIT", "name": "http-errors", "repository": {"type": "git", "url": "git+https://github.com/jshttp/http-errors.git"}, "version": "1.7.2"}