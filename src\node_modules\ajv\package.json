{"author": {"name": "<PERSON><PERSON><PERSON>"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/ajv"}, "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "description": "Another JSON Schema Validator", "devDependencies": {"ajv-async": "^1.0.0", "bluebird": "^3.5.3", "brfs": "^2.0.0", "browserify": "^16.2.0", "chai": "^4.0.1", "coveralls": "^3.0.1", "del-cli": "^3.0.0", "dot": "^1.0.3", "eslint": "^7.3.1", "gh-pages-generator": "^0.2.3", "glob": "^7.0.0", "if-node-version": "^1.0.0", "js-beautify": "^1.7.3", "jshint": "^2.10.2", "json-schema-test": "^2.0.0", "karma": "^5.0.0", "karma-chrome-launcher": "^3.0.0", "karma-mocha": "^2.0.0", "karma-sauce-launcher": "^4.1.3", "mocha": "^8.0.1", "nyc": "^15.0.0", "pre-commit": "^1.1.1", "require-globify": "^1.3.0", "typescript": "^3.9.5", "uglify-js": "^3.6.9", "watch": "^1.0.0"}, "files": ["lib/", "dist/", "scripts/", "LICENSE", ".tonic_example.js"], "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}, "homepage": "https://github.com/ajv-validator/ajv", "license": "MIT", "main": "lib/ajv.js", "name": "ajv", "repository": {"type": "git", "url": "git+https://github.com/ajv-validator/ajv.git"}, "tonicExampleFilename": ".tonic_example.js", "typings": "lib/ajv.d.ts", "version": "6.12.6"}