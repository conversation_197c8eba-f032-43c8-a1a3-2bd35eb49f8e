{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "description": "Utility to parse a string bytes to bytes and vice-versa", "devDependencies": {"eslint": "5.12.1", "mocha": "5.2.0", "nyc": "13.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "homepage": "https://github.com/visionmedia/bytes.js#readme", "license": "MIT", "name": "bytes", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "version": "3.1.0"}