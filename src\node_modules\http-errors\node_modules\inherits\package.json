{"browser": "./inherits_browser.js", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "devDependencies": {"tap": "^7.1.0"}, "files": ["inherits.js", "inherits_browser.js"], "homepage": "https://github.com/isaacs/inherits#readme", "license": "ISC", "main": "./inherits.js", "name": "inherits", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "version": "2.0.3"}