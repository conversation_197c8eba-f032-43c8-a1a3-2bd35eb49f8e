{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/"}, "dependencies": {}, "description": "Detect whether or not an object is a Typed Array", "devDependencies": {"tape": "^2.13.1"}, "homepage": "https://github.com/hughsk/is-typedarray", "license": "MIT", "main": "index.js", "name": "is-typedarray", "repository": {"type": "git", "url": "git://github.com/hughsk/is-typedarray.git"}, "version": "1.0.0"}