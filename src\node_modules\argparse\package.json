{"dependencies": {"sprintf-js": "~1.0.2"}, "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "devDependencies": {"eslint": "^2.13.1", "istanbul": "^0.4.5", "mocha": "^3.1.0", "ndoc": "^5.0.1"}, "files": ["index.js", "lib/"], "homepage": "https://github.com/nodeca/argparse#readme", "license": "MIT", "name": "<PERSON><PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "version": "1.0.10"}