{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "dependencies": {}, "description": "HTTP Agent that keeps socket connections alive between keep-alive requests. Formerly part of mikeal/request, now a standalone module.", "devDependencies": {}, "engines": {"node": "*"}, "homepage": "https://github.com/mikeal/forever-agent#readme", "license": "Apache-2.0", "main": "index.js", "name": "forever-agent", "optionalDependencies": {}, "repository": {"url": "git+https://github.com/mikeal/forever-agent.git"}, "version": "0.6.1"}