{"author": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Traverse JSON Schema passing each schema object to callback", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "license": "MIT", "main": "index.js", "name": "json-schema-traverse", "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "version": "0.4.1"}