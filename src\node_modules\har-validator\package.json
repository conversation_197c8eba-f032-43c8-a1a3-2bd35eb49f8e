{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "devDependencies": {"tap": "^14.10.8"}, "engines": {"node": ">=6"}, "files": ["lib"], "homepage": "https://github.com/ahmadnassri/node-har-validator", "license": "MIT", "main": "lib/promise.js", "name": "har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/node-har-validator.git"}, "version": "5.1.5"}