{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "description": "Flatten an array of nested arrays into a single flat array", "devDependencies": {"istanbul": "^0.3.13", "mocha": "^2.2.4", "pre-commit": "^1.0.7", "standard": "^3.7.3"}, "files": ["array-flatten.js", "LICENSE"], "homepage": "https://github.com/blakeembrey/array-flatten", "license": "MIT", "main": "array-flatten.js", "name": "array-flatten", "repository": {"type": "git", "url": "git://github.com/blakeembrey/array-flatten.git"}, "version": "1.1.1"}