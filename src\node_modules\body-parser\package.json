{"dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "description": "Node.js body parsing middleware", "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "methods": "1.1.2", "mocha": "6.1.4", "safe-buffer": "5.1.2", "supertest": "4.0.2"}, "engines": {"node": ">= 0.8"}, "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/body-parser#readme", "license": "MIT", "name": "body-parser", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "version": "1.19.0"}