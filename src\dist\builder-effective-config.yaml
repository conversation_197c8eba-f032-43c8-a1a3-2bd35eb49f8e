directories:
  output: dist
  buildResources: build
electronVersion: 11.0.0
productName: tidalab
appId: com.tidalab
copyright: ©2023 tidalab, Inc.
extraResources:
  - from: ../libs/${env.target}-${arch}
    to: ./libs/${env.target}-${arch}
  - from: ../libs/geo
    to: ./libs/${env.target}-${arch}
  - from: ../libs/config
    to: ./libs/${env.target}-${arch}
files:
  - filter:
      - main.js
      - package.json
      - '**/*'
dmg:
  contents:
    - x: 410
      'y': 150
      type: link
      path: /Applications
    - x: 130
      'y': 150
      type: file
mac:
  icon: ../res/icon.icns
win:
  icon: ../res/icon.ico
  target:
    - nsis
nsis:
  oneClick: false
  perMachine: true
  allowToChangeInstallationDirectory: true
