{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "description": "ECMAScript parsing infrastructure for multipurpose analysis", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.1.0", "istanbul": "~0.4.0", "json-diff": "~0.3.1", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.2.3", "karma-edge-launcher": "~0.2.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.3.0", "karma-safari-launcher": "~1.0.0", "karma-safaritechpreview-launcher": "~0.0.4", "karma-sauce-launcher": "~1.1.0", "lodash": "~3.10.1", "mocha": "~3.2.0", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.2", "temp": "~0.8.3", "tslint": "~5.1.0", "typescript": "~2.3.2", "typescript-formatter": "~5.1.3", "unicode-8.0.0": "~0.7.0", "webpack": "~1.14.0"}, "engines": {"node": ">=4"}, "files": ["bin", "dist/esprima.js"], "homepage": "http://esprima.org", "license": "BSD-2-<PERSON><PERSON>", "main": "dist/esprima.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://ariya.ofilabs.com"}], "name": "esprima", "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "version": "4.0.1"}