{"$schema": "http://json-schema.org/draft-04/schema#", "id": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"type": {"type": [{"id": "#simple-type", "type": "string", "enum": ["object", "array", "string", "number", "boolean", "null", "any"]}, "array"], "items": {"type": [{"$ref": "#simple-type"}, {"$ref": "#"}]}, "uniqueItems": true, "default": "any"}, "disallow": {"type": ["string", "array"], "items": {"type": ["string", {"$ref": "#"}]}, "uniqueItems": true}, "extends": {"type": [{"$ref": "#"}, "array"], "items": {"$ref": "#"}, "default": {}}, "enum": {"type": "array", "minItems": 1, "uniqueItems": true}, "minimum": {"type": "number"}, "maximum": {"type": "number"}, "exclusiveMinimum": {"type": "boolean", "default": false}, "exclusiveMaximum": {"type": "boolean", "default": false}, "divisibleBy": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 1}, "minLength": {"type": "integer", "minimum": 0, "default": 0}, "maxLength": {"type": "integer"}, "pattern": {"type": "string"}, "items": {"type": [{"$ref": "#"}, "array"], "items": {"$ref": "#"}, "default": {}}, "additionalItems": {"type": [{"$ref": "#"}, "boolean"], "default": {}}, "minItems": {"type": "integer", "minimum": 0, "default": 0}, "maxItems": {"type": "integer", "minimum": 0}, "uniqueItems": {"type": "boolean", "default": false}, "properties": {"type": "object", "additionalProperties": {"$ref": "#"}, "default": {}}, "patternProperties": {"type": "object", "additionalProperties": {"$ref": "#"}, "default": {}}, "additionalProperties": {"type": [{"$ref": "#"}, "boolean"], "default": {}}, "minProperties": {"type": "integer", "minimum": 0, "default": 0}, "maxProperties": {"type": "integer", "minimum": 0}, "required": {"type": "array", "items": {"type": "string"}}, "dependencies": {"type": "object", "additionalProperties": {"type": ["string", "array", {"$ref": "#"}], "items": {"type": "string"}}, "default": {}}, "id": {"type": "string"}, "$ref": {"type": "string"}, "$schema": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "default": {"type": "any"}}, "dependencies": {"exclusiveMinimum": "minimum", "exclusiveMaximum": "maximum"}, "default": {}}