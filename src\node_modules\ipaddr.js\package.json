{"author": {"name": "whitequark", "email": "<EMAIL>"}, "dependencies": {}, "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "devDependencies": {"coffee-script": "~1.12.6", "nodeunit": "^0.11.3", "uglify-js": "~3.0.19"}, "directories": {"lib": "./lib"}, "engines": {"node": ">= 0.10"}, "files": ["lib/", "LICENSE", "ipaddr.min.js"], "homepage": "https://github.com/whitequark/ipaddr.js#readme", "license": "MIT", "main": "./lib/ipaddr.js", "name": "ipaddr.js", "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "types": "./lib/ipaddr.js.d.ts", "version": "1.9.1"}