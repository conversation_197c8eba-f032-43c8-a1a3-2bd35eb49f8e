{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "description": "JSON Schema for HTTP Archive (HAR)", "devDependencies": {"ajv": "^5.0.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "engines": {"node": ">=4"}, "files": ["lib"], "homepage": "https://github.com/ahmadnassri/har-schema", "license": "ISC", "main": "lib/index.js", "name": "har-schema", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-schema.git"}, "version": "2.0.0"}