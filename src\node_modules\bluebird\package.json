{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/petkaantonov/"}, "browser": "./js/browser/bluebird.js", "description": "Full featured Promises/A+ implementation with exceptionally good performance", "devDependencies": {"acorn": "^6.0.2", "acorn-walk": "^6.1.0", "baconjs": "^0.7.43", "bluebird": "^2.9.2", "body-parser": "^1.10.2", "browserify": "^8.1.1", "cli-table": "~0.3.1", "co": "^4.2.0", "cross-spawn": "^0.2.3", "glob": "^4.3.2", "grunt-saucelabs": "~8.4.1", "highland": "^2.3.0", "istanbul": "^0.3.5", "jshint": "^2.6.0", "jshint-stylish": "~0.2.0", "kefir": "^2.4.1", "mkdirp": "~0.5.0", "mocha": "~2.1", "open": "~0.0.5", "optimist": "~0.6.1", "rimraf": "~2.2.6", "rx": "^2.3.25", "serve-static": "^1.7.1", "sinon": "~1.7.3", "uglify-js": "~2.4.16"}, "files": ["js/browser", "js/release", "LICENSE"], "homepage": "https://github.com/petkaantonov/bluebird", "license": "MIT", "main": "./js/release/bluebird.js", "name": "bluebird", "repository": {"type": "git", "url": "git://github.com/petkaantonov/bluebird.git"}, "version": "3.7.2", "webpack": "./js/release/bluebird.js"}