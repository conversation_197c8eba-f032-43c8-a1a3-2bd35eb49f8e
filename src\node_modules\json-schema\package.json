{"author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "devDependencies": {"vows": "*"}, "directories": {"lib": "./lib"}, "homepage": "https://github.com/kriszyp/json-schema#readme", "licenses": [{"type": "AFLv2.1", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L43"}, {"type": "BSD", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L13"}], "main": "./lib/validate.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}], "name": "json-schema", "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "version": "0.2.3"}