{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "dependencies": {}, "description": "Buffers events from a stream until you are ready to handle them.", "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/felixge/node-delayed-stream", "license": "MIT", "main": "./lib/delayed_stream", "name": "delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "version": "1.0.0"}