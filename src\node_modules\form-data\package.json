{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "browser": "./lib/browser", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "engines": {"node": ">= 0.12"}, "homepage": "https://github.com/form-data/form-data#readme", "license": "MIT", "main": "./lib/form_data", "name": "form-data", "pre-commit": ["lint", "ci-test", "check"], "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "version": "2.3.3"}