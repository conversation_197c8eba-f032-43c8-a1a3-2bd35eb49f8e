{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "description": "Like JSON.stringify, but doesn't blow up on circular refs.", "devDependencies": {"mocha": ">= 2.1.0 < 3", "must": ">= 0.12 < 0.13", "sinon": ">= 1.12.2 < 2"}, "homepage": "https://github.com/isaacs/json-stringify-safe", "license": "ISC", "main": "stringify.js", "name": "json-stringify-safe", "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe.git"}, "version": "5.0.1"}