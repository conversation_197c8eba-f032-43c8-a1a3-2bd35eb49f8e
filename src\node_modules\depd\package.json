{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "lib/browser/index.js", "description": "Deprecate all the things", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "3.19.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "homepage": "https://github.com/dougwilson/nodejs-depd#readme", "license": "MIT", "name": "depd", "repository": {"type": "git", "url": "git+https://github.com/dougwilson/nodejs-depd.git"}, "version": "1.1.2"}