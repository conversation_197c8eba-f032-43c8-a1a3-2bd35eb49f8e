{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "HTTP server cookie parsing and serialization", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "5.16.0", "eslint-plugin-markdown": "1.0.0", "istanbul": "0.4.5", "mocha": "6.1.4"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/jshttp/cookie#readme", "license": "MIT", "name": "cookie", "repository": {"type": "git", "url": "git+https://github.com/jshttp/cookie.git"}, "version": "0.4.0"}