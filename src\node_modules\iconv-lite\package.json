{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "description": "Convert character encodings in pure javascript.", "devDependencies": {"async": "*", "errto": "*", "iconv": "*", "istanbul": "*", "mocha": "^3.1.0", "request": "~2.87.0", "semver": "*", "unorm": "*"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/ashtuchkin/iconv-lite", "license": "MIT", "main": "./lib/index.js", "name": "iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "typings": "./lib/index.d.ts", "version": "0.4.24"}