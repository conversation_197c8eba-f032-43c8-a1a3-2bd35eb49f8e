{"name": "AfeiCloud", "main": "main.js", "version": "4.1.10", "author": "<PERSON>ab", "license": "CC0-1.0", "dependencies": {"express": "^4.17.1", "js-yaml": "^3.14.0", "kill-port": "^1.6.1", "request": "^2.88.2", "request-promise": "^4.2.5", "tcp-ping": "^0.1.1"}, "build": {"electronVersion": "11.0.0", "productName": "<PERSON>ab", "appId": "com.tidalab", "copyright": "©2023 tidalab, Inc.", "directories": {"output": "build"}, "extraResources": [{"from": "../libs/${env.target}-${arch}", "to": "./libs/${env.target}-${arch}"}, {"from": "../libs/geo", "to": "./libs/${env.target}-${arch}"}, {"from": "../libs/config", "to": "./libs/${env.target}-${arch}"}], "files": ["main.js", "package.json", "**/*"], "dmg": {"contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"icon": "../res/icon.icns"}, "win": {"icon": "../res/icon.ico", "target": ["nsis"]}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true}}}