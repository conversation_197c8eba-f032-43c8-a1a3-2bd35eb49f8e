{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "description": "Sign and unsign cookies", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "license": "MIT", "main": "index", "name": "cookie-signature", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-cookie-signature.git"}, "version": "1.0.6"}